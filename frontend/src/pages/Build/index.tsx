import React, { useState, useEffect, useRef } from 'react';
import {
  Container,
  Paper,
  Typography,
  Box,
  TextField,
  Button,
  IconButton,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Send as SendIcon,
  ArrowBack as ArrowBackIcon,
  Psychology as AIIcon,
  Folder as WorkspaceIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import Header from '../../components/Header';
// import SphereEngineWorkspace from '../../components/SphereEngineWorkspace'; // Commented out for Monaco Editor replacement
import MonacoWorkspace from '../../components/MonacoWorkspace';
import { interviewAPI, buildAPI, chatAPI, ChatMessage } from '../../utils/api';
import { ProjectData, UserData, transformNewSchemaToFlatAnswers } from '../../utils/dataTransform';

// Monaco Editor workspace configuration
// const SPHERE_ENGINE_PROJECT_ID = '025a0cfd469e4dc49b5bfbccc29539c4'; // Commented out for Monaco Editor replacement

interface LocationState {
  prompt?: string;
  projectData?: ProjectData;
  projectDataOld?: any;
  functionalAnswers?: { [key: string]: string };
  technicalAnswers?: { [key: string]: string };
  user?: UserData;
  shouldStartBuild?: boolean; // Flag to indicate fresh build should be started immediately
  llmResponse?: {
    success: boolean;
    content: string;
    buildResultUuid?: string;
    extractedContent?: {
      description?: string;
      codeBlocks: any[];
      projectStructure?: any;
      deploymentInstructions?: string;
      additionalSections?: { [key: string]: string };
    };
    metadata: any;
  };
  buildResponse?: {
    success: boolean;
    message: string;
    buildResult?: {
      uuid: string;
      description?: string;
      deploymentInstructions?: string;
      additionalSections?: { [key: string]: string };
      status: string;
      codeBlocks: any[];
      projectStructure?: any;
    };
    workspace?: {
      workspaceId: string;
      workspaceUrl: string;
      status: string;
    };
    metadata: any;
  };
  llmError?: string;
}

interface InterviewData {
  uuid: string;
  user: UserData;
  projectData: ProjectData;
}

// ChatMessage interface is now imported from utils/api

const Build: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { uuid } = useParams<{ uuid?: string }>();
  const [prompt, setPrompt] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [interviewData, setInterviewData] = useState<InterviewData | null>(null);
  const [isLoading, setIsLoading] = useState(!!uuid);
  const [error, setError] = useState<string | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [projectStructure, setProjectStructure] = useState<any>(null);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [hasCodeGenerated, setHasCodeGenerated] = useState(false);
  const [currentWorkspaceId, setCurrentWorkspaceId] = useState<string | null>(null);
  const [existingBuildResult, setExistingBuildResult] = useState<any>(null);
  const [currentBuildResultUuid, setCurrentBuildResultUuid] = useState<string | null>(null);

  const [shouldRefreshWorkspace, setShouldRefreshWorkspace] = useState(false);
  const [hasShownInitialMessages, setHasShownInitialMessages] = useState(false);
  const [chatHistoryRestored, setChatHistoryRestored] = useState(false);
  const [isRestoringFromDatabase, setIsRestoringFromDatabase] = useState(false);
  const [isChatboxExpanded, setIsChatboxExpanded] = useState(true); // New state for chatbox collapse/expand

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const previousUuidRef = useRef<string | undefined>(undefined);

  // Get data from location state (passed from Interview page)
  const locationState = location.state as LocationState;
  const userPrompt = interviewData?.user?.prompt || locationState?.prompt;

  // Get project data in new format or convert from old format
  const projectData = interviewData?.projectData || locationState?.projectData;
  const userData = interviewData?.user || locationState?.user;

  // Function to extract files from LLM response
  const extractFilesFromResponse = (content: string): { [path: string]: string } => {
    const files: { [path: string]: string } = {};

    // Extract code blocks with file paths
    const codeBlockRegex = /```(?:(\w+)\s+)?(?:(?:###\s*)?([^\n]+\.[\w]+)[\s\n]*)?\n([\s\S]*?)```/g;
    let match;

    while ((match = codeBlockRegex.exec(content)) !== null) {
      const [, language, filePath, code] = match;

      if (filePath && code.trim()) {
        // Clean up the file path
        const cleanPath = filePath.replace(/^###\s*/, '').trim();
        files[cleanPath] = code.trim();
      }
    }

    return files;
  };

  // DEBUG: Log location state and LLM response
  console.log('🔍 Build page state:', {
    hasLocationState: !!locationState,
    locationStateKeys: locationState ? Object.keys(locationState) : [],
    hasLlmResponse: !!locationState?.llmResponse,
    llmResponseSuccess: locationState?.llmResponse?.success,
    hasExtractedContent: !!locationState?.llmResponse?.extractedContent,
    extractedContentKeys: locationState?.llmResponse?.extractedContent ? Object.keys(locationState.llmResponse.extractedContent) : [],
    extractedContent: locationState?.llmResponse?.extractedContent,
    messagesLength: messages.length,
    uuid,
    hasShownInitialMessages
  });

  // FIXED: Load chat history from database OR wait for initial build messages
  useEffect(() => {
    if (!uuid) return;

    const loadChatHistory = async () => {
      try {
        console.log('💬 Loading chat history from database for UUID:', uuid);
        setIsRestoringFromDatabase(true); // Prevent saves during restoration

        const response = await fetch(`http://localhost:3001/api/llm/build-exists/${uuid}`);
        const data = await response.json();

        if (data.success && data.exists && data.buildResult?.chatHistory?.length > 0) {
          console.log('✅ Found existing chat history in database:', data.buildResult.chatHistory.length, 'messages');
          const restoredMessages = data.buildResult.chatHistory.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          }));
          setMessages(restoredMessages);
          setHasShownInitialMessages(true);
          setChatHistoryRestored(true);
          setExistingBuildResult(data.buildResult);
          setHasCodeGenerated(true);
          setIsGeneratingCode(false);
          setCurrentBuildResultUuid(data.buildResult.uuid); // Set the UUID for future saves
          console.log('✅ Chat history loaded from database');

          // Allow saves again after a short delay to ensure restoration is complete
          setTimeout(() => {
            setIsRestoringFromDatabase(false);
          }, 1000);
          return;
        }

        console.log('📝 No existing chat history found - this is a new build');
        setIsRestoringFromDatabase(false);
        // For new builds, we'll wait for LLM response or other sources to create initial messages
        // Don't create any messages here - let the other useEffects handle initial message creation

      } catch (error) {
        console.error('❌ Error loading chat history:', error);
        setIsRestoringFromDatabase(false);
      }
    };

    loadChatHistory();
  }, [uuid]); // Only depend on UUID

  // REMOVED: This useEffect was causing conflicts with the unified build response handling
  // The chat history is now properly handled in the buildWithArchive function

  // FALLBACK: Add a test message after 300 seconds if no messages are shown
  // BUT only if we're not restoring from database and don't have initial messages
  useEffect(() => {
    const timer = setTimeout(() => {
      if (messages.length === 0 && !hasShownInitialMessages && !existingBuildResult && !chatHistoryRestored && !isGeneratingCode) {
        console.log('🚨 FALLBACK: No messages shown after 5 seconds, adding test message');
        setMessages([{
          id: 'fallback-test',
          type: 'ai',
          content: '🧪 FALLBACK: Build page loaded but no LLM messages were displayed. This indicates an issue with the message flow.',
          timestamp: new Date()
        }]);
      } else {
        console.log('⏭️ FALLBACK: Skipping fallback message because:', {
          messagesLength: messages.length,
          hasShownInitialMessages,
          hasExistingBuildResult: !!existingBuildResult,
          chatHistoryRestored,
          isGeneratingCode
        });
      }
    }, 300000); // Increased to 300 seconds to give more time for unified build

    return () => clearTimeout(timer);
  }, [messages.length, hasShownInitialMessages, existingBuildResult, chatHistoryRestored, isGeneratingCode]);



  // Debounced save to avoid conflicts - only save when messages change and we're not submitting or restoring
  useEffect(() => {
    // Don't save if we're still restoring chat history or if we just loaded from database
    if (currentBuildResultUuid && messages.length > 0 && !isSubmitting && !chatHistoryRestored && !isRestoringFromDatabase && hasShownInitialMessages) {
      const timeoutId = setTimeout(() => {
        console.log('💾 Debounced save - chat history after message changes:', {
          buildResultUuid: currentBuildResultUuid,
          messageCount: messages.length,
          isSubmitting,
          chatHistoryRestored,
          isRestoringFromDatabase,
          hasShownInitialMessages
        });

        saveChatHistory(currentBuildResultUuid, messages).catch(error => {
          console.error('❌ Error in debounced save:', error);
        });
      }, 3000); // 3 second delay to avoid conflicts with backend

      return () => clearTimeout(timeoutId);
    }
  }, [currentBuildResultUuid, messages, isSubmitting, chatHistoryRestored, isRestoringFromDatabase, hasShownInitialMessages]);

  // Auto-scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    console.log('📜 Messages changed, new length:', messages.length);
    console.log('📜 Message contents:', messages.map(m => `${m.type}: ${m.content.substring(0, 30)}...`));
    scrollToBottom();
  }, [messages]);

  // Load interview data if UUID is provided
  useEffect(() => {
    let isMounted = true;

    // Clear previous state when UUID changes (but only if UUID actually changed)
    console.log('🔄 Frontend: UUID changed to:', uuid, 'previous:', previousUuidRef.current);

    // Only clear state if this is a different UUID
    const shouldClearState = previousUuidRef.current !== uuid;

    if (shouldClearState) {
      console.log('🧹 Clearing state for new UUID');
      setMessages([]);
      setExistingBuildResult(null);
      setCurrentBuildResultUuid(null);
      setHasCodeGenerated(false);
      setIsGeneratingCode(false);
      setError(null);
      setHasShownInitialMessages(false);
      setChatHistoryRestored(false); // IMPORTANT: Reset this for new builds
      setIsRestoringFromDatabase(false); // Reset restoration flag

      // Update the ref to track current UUID
      previousUuidRef.current = uuid;
    } else {
      console.log('⏭️ Keeping existing state for same UUID');
    }



    const loadInterviewData = async () => {
      if (!isMounted) return;

      if (!uuid) {
        setIsLoading(false);
        // Check if we have data from location state, otherwise show error
        if (!userPrompt && !locationState?.prompt) {
          setError('No interview session found. Please start from the interview page to generate code.');
          return;
        }
        // Start code generation immediately if no UUID but we have prompt data
        if (userPrompt && !isGeneratingCode && !hasCodeGenerated) {
          handleInitialCodeGeneration();
        }
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        const response = await interviewAPI.get(uuid);

        if (!isMounted) return;

        if (response.success) {
          setInterviewData(response.data);

          // Check if this is a fresh build request from interview page
          const shouldStartBuild = locationState?.shouldStartBuild;

          if (shouldStartBuild) {
            console.log('🚀 Frontend: Fresh build request from interview page, starting build immediately...');

            // Start the build process immediately for better UX
            const combinedData = {
              ...response.data,
              locationState: locationState
            };

            const effectivePrompt = locationState?.prompt || response.data?.user?.prompt || 'Generate project based on interview responses';
            buildWithArchive(effectivePrompt, combinedData?.projectData || response.data?.projectData, combinedData?.user || response.data?.user);
          } else {
            // Check for existing build in database before starting new build
            console.log('🔍 Frontend: Checking for existing build in database before starting new build...');
            console.log('🔍 Frontend: Current state:', {
              uuid,
              isGeneratingCode,
              hasCodeGenerated,
              existingBuildResult: !!existingBuildResult,
              chatHistoryRestored
            });

            try {
              const existingBuildResponse = await fetch(`http://localhost:3001/api/llm/build-exists/${uuid}`);
              const existingBuildData = await existingBuildResponse.json();

              console.log('🔍 Frontend: Existing build check result:', existingBuildData);

              if (existingBuildData.success && existingBuildData.exists) {
                console.log('✅ Frontend: Found existing build in database, skipping new build');
                console.log('✅ Frontend: Existing build has chat history:', existingBuildData.buildResult?.chatHistory?.length || 0, 'messages');
                setHasCodeGenerated(true);
                setIsGeneratingCode(false);

                // Set project structure if available
                if (existingBuildData.buildResult?.projectStructure) {
                  setProjectStructure(existingBuildData.buildResult.projectStructure);
                }

                // The chat history will be loaded by the loadChatHistory useEffect
                console.log('✅ Frontend: Existing build setup complete, waiting for chat history to load');
              } else if (!isGeneratingCode) {
                console.log('✅ Frontend: No existing build found, starting new build with archive for interview:', uuid);

                const combinedData = {
                  ...response.data,
                  locationState: locationState
                };

                const effectivePrompt = locationState?.prompt || response.data?.user?.prompt || 'Generate project based on interview responses';
                buildWithArchive(effectivePrompt, combinedData?.projectData || response.data?.projectData, combinedData?.user || response.data?.user);
              } else {
                console.log('⏭️ Frontend: Skipping build - isGeneratingCode:', isGeneratingCode);
              }
            } catch (checkError) {
              console.error('❌ Error checking for existing build:', checkError);
              // If check fails, proceed with new build as fallback
              if (!isGeneratingCode) {
                console.log('⚠️ Frontend: Check failed, proceeding with new build as fallback');
                const combinedData = {
                  ...response.data,
                  locationState: locationState
                };
                const effectivePrompt = locationState?.prompt || response.data?.user?.prompt || 'Generate project based on interview responses';
                buildWithArchive(effectivePrompt, combinedData?.projectData || response.data?.projectData, combinedData?.user || response.data?.user);
              }
            }
          }
        } else {
          setError('Failed to load interview configuration');
        }
      } catch (error: any) {
        console.error('Error loading interview data:', error);
        if (isMounted) {
          setError(error.response?.data?.message || 'Failed to load interview configuration');
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadInterviewData();

    return () => {
      isMounted = false;
    };
  }, [uuid]); // Remove userPrompt dependency to prevent multiple runs



  // Save chat history to database (debounced to avoid conflicts)
  const saveChatHistory = async (buildResultUuid: string, chatHistory: ChatMessage[]) => {
    try {
      console.log('💾 Saving chat history:', {
        buildResultUuid,
        messageCount: chatHistory.length,
        messageTypes: chatHistory.map(m => m.type),
        lastMessage: chatHistory[chatHistory.length - 1]?.content?.substring(0, 50) + '...'
      });
      await chatAPI.updateChatHistory(buildResultUuid, chatHistory);
      console.log('✅ Chat history saved successfully');
    } catch (error) {
      console.error('❌ Error saving chat history:', error);
    }
  };

  // Simple function to create messages from extracted content
  const createMessagesFromExtractedContent = (extractedContent: any): ChatMessage[] => {
    console.log('📝 Creating messages from extracted content:', {
      hasDescription: !!extractedContent.description,
      hasDeploymentInstructions: !!extractedContent.deploymentInstructions,
      hasAdditionalSections: !!extractedContent.additionalSections
    });

    const messages: ChatMessage[] = [];
    let messageId = 1;

    // 1. Initial welcome message
    messages.push({
      id: messageId.toString(),
      type: 'ai',
      content: '🚀 Starting project generation...',
      timestamp: new Date()
    });
    messageId++;

    // 2. Success message with description (if available)
    if (extractedContent.description) {
      messages.push({
        id: messageId.toString(),
        type: 'ai',
        content: `🎉 Project generated successfully!\n\n${extractedContent.description}`,
        timestamp: new Date()
      });
      messageId++;

      // Set project structure if available
      if (extractedContent.projectStructure) {
        setProjectStructure(extractedContent.projectStructure);
      }
    }

    // 3. Additional sections (if any)
    if (extractedContent.additionalSections && Object.keys(extractedContent.additionalSections).length > 0) {
      for (const [sectionName, sectionContent] of Object.entries(extractedContent.additionalSections)) {
        messages.push({
          id: messageId.toString(),
          type: 'ai',
          content: `📋 **${sectionName}**\n\n${sectionContent}`,
          timestamp: new Date()
        });
        messageId++;
      }
    }

    // 4. Deployment instructions (if available)
    if (extractedContent.deploymentInstructions) {
      messages.push({
        id: messageId.toString(),
        type: 'ai',
        content: `🚀 **Ready to Deploy!**\n\n${extractedContent.deploymentInstructions}`,
        timestamp: new Date()
      });
      messageId++;
    }

    // 5. Final completion message
    messages.push({
      id: messageId.toString(),
      type: 'ai',
      content: '✅ Project generation complete! Your code is ready for review and deployment.',
      timestamp: new Date()
    });

    console.log('✅ Created', messages.length, 'messages from extracted content');
    return messages;
  };

  // Handle initial code generation when component loads
  const handleInitialCodeGeneration = async (data?: InterviewData) => {
    console.log('🚀 handleInitialCodeGeneration called with:', {
      isGeneratingCode,
      hasCodeGenerated,
      existingBuildResult: !!existingBuildResult,
      messagesLength: messages.length,
      hasLocationState: !!locationState,
      hasData: !!data,
      hasShownInitialMessages
    });

    if (isGeneratingCode || hasCodeGenerated) {
      console.log('❌ Skipping code generation - already generating or completed');
      return;
    }

    // FIXED: Don't start generation if we have an existing build with chat history
    if (existingBuildResult) {
      console.log('❌ Skipping code generation - existing build found with chat history');
      return;
    }

    console.log('✅ Starting initial code generation...');
    setIsGeneratingCode(true);

    // Check if we have LLM response data from the interview
    // ENHANCED: Check both direct locationState and data.locationState (passed from loadInterviewData)
    const effectiveLocationState = locationState || (data as any)?.locationState;
    const llmResponse = effectiveLocationState?.llmResponse;
    const llmError = effectiveLocationState?.llmError;

    console.log('🔍 LLM Response check:', {
      hasLocationState: !!locationState,
      hasDataLocationState: !!(data as any)?.locationState,
      hasEffectiveLocationState: !!effectiveLocationState,
      hasLlmResponse: !!llmResponse,
      llmSuccess: llmResponse?.success,
      hasExtractedContent: !!llmResponse?.extractedContent,
      llmError,
      extractedContentKeys: llmResponse?.extractedContent ? Object.keys(llmResponse.extractedContent) : [],
      hasShownInitialMessages
    });

    if (llmError && !existingBuildResult && !chatHistoryRestored) {
      const errorMessage = {
        id: '1',
        type: 'ai' as const,
        content: `Welcome to the Build environment! ⚠️ There was an issue generating content: ${llmError}. Using fallback generation...`,
        timestamp: new Date()
      };
      setMessages([errorMessage]);
      console.log('📝 Set error message');
    } else if (llmError && existingBuildResult) {
      console.log('📝 SKIP: Not setting error message - existing build result found with chat history');

      // Save error message immediately if we have a buildResultUuid later
      // This will be handled in fetchCodeStructure
    } else if (llmResponse?.success && llmResponse.extractedContent && !hasShownInitialMessages && !existingBuildResult && !chatHistoryRestored) {
      // SIMPLIFIED: Create messages from LLM extracted content (only for new builds)
      // BUT only if we don't have existing chat history restored
      console.log('📝 Creating messages from LLM extracted content (NEW BUILD)');
      console.log('📝 Extracted content:', llmResponse.extractedContent);

      // Mark that we're showing initial messages to prevent duplicates
      setHasShownInitialMessages(true);

      // Create all messages from extracted content immediately
      const extractedMessages = createMessagesFromExtractedContent(llmResponse.extractedContent);
      setMessages(extractedMessages);
      console.log('✅ Set', extractedMessages.length, 'messages from extracted content');

      // Extract and populate Monaco workspace with initial files
      if (llmResponse.extractedContent?.codeBlocks && uuid) {
        const initialFiles: { [path: string]: string } = {};

        llmResponse.extractedContent.codeBlocks.forEach((block: any) => {
          if (block.filename && block.content) {
            initialFiles[block.filename] = block.content;
          }
        });

        if (Object.keys(initialFiles).length > 0) {
          console.log('📁 Populating Monaco workspace with initial files:', Object.keys(initialFiles));

          // Dispatch event to Monaco workspace
          window.dispatchEvent(new CustomEvent('monaco-workspace-update', {
            detail: { files: initialFiles }
          }));
        }
      }
    } else if (existingBuildResult) {
      console.log('📝 SKIP: Not creating messages from LLM content - existing build result found with chat history');
    } else if (!existingBuildResult && !chatHistoryRestored) {
      const welcomeMessage = {
        id: '1',
        type: 'ai' as const,
        content: 'Welcome to the Build environment! I\'m starting to generate your project based on your requirements...',
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
      console.log('📝 Set welcome message');

      // Save welcome message immediately if we have a buildResultUuid later
      // This will be handled in fetchCodeStructure
    }

    // FALLBACK: Ensure at least one message is always shown
    // BUT only if we don't have existing chat history restored
    if (messages.length === 0 && !existingBuildResult && !chatHistoryRestored) {
      console.log('🚨 FALLBACK: No messages found, adding fallback message');
      const fallbackMessage = {
        id: 'fallback-1',
        type: 'ai' as const,
        content: '🚀 Initializing your project build environment...',
        timestamp: new Date()
      };
      setMessages([fallbackMessage]);
    } else if (messages.length === 0 && existingBuildResult) {
      console.log('🚨 FALLBACK: No messages but existing build result found - chat history should be restored');
    }

    try {
      const effectivePrompt = data?.user?.prompt || userPrompt || 'Web application';

      // If we already have LLM response data, skip the generation and proceed to fetchCodeStructure
      if (llmResponse?.success && llmResponse.extractedContent) {
        console.log('Using existing LLM response data, proceeding to fetch code structure...');

        // Proceed directly to buildWithArchive which will handle progressive messages
        setTimeout(() => {
          buildWithArchive(effectivePrompt, data?.projectData || projectData, data?.user || userData);
          setIsGeneratingCode(false);
          setHasCodeGenerated(true);
        }, 500);

        return;
      }

      // Start the chat stream for generation progress (fallback)
      const response = await buildAPI.generateCode({
        interviewUuid: uuid,
        prompt: effectivePrompt,
        projectData: data?.projectData || projectData,
        userData: data?.user || userData
      });

      if (!response.ok) {
        throw new Error('Failed to generate code');
      }

      const reader = response.body?.getReader();
      const decoder = new TextDecoder();

      if (reader) {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));

                if (data.type === 'ai' && data.content) {
                  const messageId = `${Date.now()}-${Math.random()}`;
                  console.log('Adding AI message:', data.content);
                  const newMessage = {
                    id: messageId,
                    type: 'ai' as const,
                    content: data.content,
                    timestamp: new Date()
                  };

                  setMessages(prev => {
                    // Check if this message already exists (duplicate prevention)
                    const isDuplicate = prev.some(msg =>
                      msg.content === data.content &&
                      msg.type === 'ai' &&
                      Math.abs(new Date().getTime() - msg.timestamp.getTime()) < 5000 // within 5 seconds
                    );

                    if (isDuplicate) {
                      console.log('Skipping duplicate message:', data.content);
                      return prev;
                    }

                    const updatedMessages = [...prev, newMessage];

                    // Save to database immediately if we have a build result UUID
                    if (currentBuildResultUuid) {
                      console.log('💾 Saving streaming message to database');
                      saveChatHistory(currentBuildResultUuid, updatedMessages).catch(error => {
                        console.error('❌ Error saving streaming message:', error);
                      });
                    }

                    return updatedMessages;
                  });
                } else if (data.success && data.message === 'Code generation process completed') {
                  // Generation completed, now build with archive
                  console.log('Generation completed, building with archive...');
                  buildWithArchive(effectivePrompt, data?.projectData || projectData, data?.user || userData);
                }
              } catch (e) {
                console.error('Error parsing SSE data:', e);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error generating code:', error);
      const errorMessage = {
        id: Date.now().toString(),
        type: 'ai' as const,
        content: 'Sorry, there was an error generating your code. Please try again.',
        timestamp: new Date()
      };

      setMessages(prev => {
        const updatedMessages = [...prev, errorMessage];

        // Save error message to database
        if (currentBuildResultUuid) {
          console.log('💾 Saving error message to database');
          saveChatHistory(currentBuildResultUuid, updatedMessages).catch(error => {
            console.error('❌ Error saving error message:', error);
          });
        }

        return updatedMessages;
      });
    } finally {
      setIsGeneratingCode(false);
    }
  };

  // Updated function to use unified build (safer, no overwrites)
  const buildWithArchive = async (prompt: string, projectData?: any, userData?: any) => {
    // Declare thinking message outside try block so it's accessible in catch
    let thinkingMessage: ChatMessage | null = null;

    try {
      console.log('📡 Frontend: Building with unified build API...');
      console.log('📡 Frontend: Interview UUID:', uuid);
      console.log('📡 Frontend: Prompt:', prompt);

      // GUARD: Check if we already have an existing build to prevent overwriting
      if (existingBuildResult || chatHistoryRestored) {
        console.log('🛡️ Frontend: GUARD - Existing build detected, skipping unified build to prevent overwrite');
        console.log('🛡️ Frontend: existingBuildResult:', !!existingBuildResult);
        console.log('🛡️ Frontend: chatHistoryRestored:', chatHistoryRestored);
        return;
      }

      // Double-check with database before proceeding
      console.log('🔍 Frontend: Double-checking database for existing build before unified build...');
      try {
        const checkResponse = await fetch(`http://localhost:3001/api/llm/build-exists/${uuid}`);
        const checkData = await checkResponse.json();

        if (checkData.success && checkData.exists) {
          console.log('🛡️ Frontend: GUARD - Database check found existing build, aborting unified build');
          console.log('🛡️ Frontend: Existing build has', checkData.buildResult?.chatHistory?.length || 0, 'chat messages');

          // Set the data we have
          setHasCodeGenerated(true);
          setIsGeneratingCode(false);
          if (checkData.buildResult?.projectStructure) {
            setProjectStructure(checkData.buildResult.projectStructure);
          }
          return;
        }
      } catch (checkError) {
        console.warn('⚠️ Frontend: Database check failed, proceeding with unified build:', checkError);
      }

      // Add "AI is thinking..." message before starting the build
      thinkingMessage = {
        id: `thinking-${Date.now()}`,
        type: 'ai',
        content: '🤖 AI is analyzing your requirements and generating code...',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, thinkingMessage!]);

      const response = await buildAPI.unifiedBuild({
        interviewUuid: uuid,
        prompt: prompt,
        projectData: projectData,
        userData: userData
      });

      console.log('📡 Frontend: Unified build response received:', {
        success: response.success,
        hasBuildResult: !!response.buildResult,
        buildResultUuid: response.buildResult?.uuid,
        hasProjectStructure: !!response.buildResult?.projectStructure,
        hasWorkspace: !!response.workspace,
        workflow: response.metadata?.workflow
      });

      // Remove the thinking message now that we have a response
      setMessages(prev => prev.filter(msg => msg.id !== thinkingMessage!.id));

      if (response.success && response.buildResult?.projectStructure) {
        console.log('Code structure built successfully:', response.buildResult.projectStructure);
        setProjectStructure(response.buildResult.projectStructure);
        setHasCodeGenerated(true);

        // ENHANCED: Set the buildResultUuid and handle progressive message display
        if (response.buildResult?.uuid) {
          console.log('💾 Setting BuildResult UUID:', response.buildResult.uuid);
          setCurrentBuildResultUuid(response.buildResult.uuid);

          // Check if the unified build response already includes chat history
          if (response.buildResult.chatHistory && response.buildResult.chatHistory.length > 0) {
            console.log('✅ Using chat history from unified build response:', response.buildResult.chatHistory.length, 'messages');
            const messagesFromResponse = response.buildResult.chatHistory.map((msg: any) => ({
              ...msg,
              timestamp: new Date(msg.timestamp)
            }));
            setMessages(messagesFromResponse);
            setHasShownInitialMessages(true);
            console.log('✅ Set', messagesFromResponse.length, 'messages from unified build response');
          } else if (!existingBuildResult && !chatHistoryRestored) {
            // Fallback: Create messages from extracted content if no chat history in response
            console.log('📝 Creating messages from unified build result (fallback)...');
            const messagesFromContent = createMessagesFromExtractedContent(response.buildResult);
            setMessages(messagesFromContent);
            setHasShownInitialMessages(true);
            console.log('✅ Set', messagesFromContent.length, 'messages from unified build result (fallback)');

            // Save initial messages to database immediately
            console.log('💾 Saving initial unified build messages to database');
            saveChatHistory(response.buildResult.uuid, messagesFromContent).catch(error => {
              console.error('❌ Error saving initial unified build messages:', error);
            });
          } else {
            console.log('📝 SKIP: Not creating messages from unified build - existing build result found with chat history');
            setHasShownInitialMessages(true);
          }

          // Populate Monaco workspace with extracted files
          if (response.buildResult?.codeBlocks && uuid) {
            const workspaceFiles: { [path: string]: string } = {};

            response.buildResult.codeBlocks.forEach((block: any) => {
              if (block.filename && block.content) {
                workspaceFiles[block.filename] = block.content;
              }
            });

            if (Object.keys(workspaceFiles).length > 0) {
              console.log('📁 Populating Monaco workspace with build result files:', Object.keys(workspaceFiles));

              // Update Monaco workspace directly
              const monacoWorkspace = (window as any)[`monacoWorkspace_${uuid}`];
              if (monacoWorkspace && monacoWorkspace.updateFiles) {
                monacoWorkspace.updateFiles(workspaceFiles);
                console.log('✅ Monaco workspace updated with build result files');
              } else {
                // Fallback: dispatch event for workspace to listen
                window.dispatchEvent(new CustomEvent('monaco-workspace-update', {
                  detail: { files: workspaceFiles }
                }));
                console.log('✅ Dispatched Monaco workspace update event');
              }
            }
          }
        }

        // REMOVED: No automatic final messages - system messages should only show for first build or user interactions
      } else {
        throw new Error('Failed to build with archive');
      }
    } catch (error) {
      console.error('Error building with archive:', error);

      // Remove the thinking message and add error message
      setMessages(prev => {
        const withoutThinking = thinkingMessage
          ? prev.filter(msg => msg.id !== thinkingMessage!.id)
          : prev;
        return [...withoutThinking, {
          id: Date.now().toString(),
          type: 'ai',
          content: 'I encountered an issue while building your project. Please try refreshing the page.',
          timestamp: new Date()
        }];
      });
    }
  };




  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || isSubmitting || !uuid) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: prompt,
      timestamp: new Date()
    };

    setMessages(prev => {
      const updatedMessages = [...prev, userMessage];

      // Save user message to database
      if (currentBuildResultUuid) {
        console.log('💾 Saving user message to database');
        saveChatHistory(currentBuildResultUuid, updatedMessages).catch(error => {
          console.error('❌ Error saving user message:', error);
        });
      }

      return updatedMessages;
    });
    setPrompt('');
    setIsSubmitting(true);

    // Add "start" message immediately
    const startMessage: ChatMessage = {
      id: `start-${Date.now()}`,
      type: 'ai',
      content: '🚀 Starting to process your request...',
      timestamp: new Date()
    };
    setMessages(prev => {
      const updatedMessages = [...prev, startMessage];

      // Save start message to database
      if (currentBuildResultUuid) {
        console.log('💾 Saving start message to database');
        saveChatHistory(currentBuildResultUuid, updatedMessages).catch(error => {
          console.error('❌ Error saving start message:', error);
        });
      }

      return updatedMessages;
    });

    try {
      // FIXED: Use fetch directly for JSON response instead of streaming
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:3001/api/build/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` })
        },
        body: JSON.stringify({
          interviewUuid: uuid,
          message: userMessage.content,
          conversationHistory: messages
        })
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const data = await response.json();

      if (data.success && data.aiMessage) {
        console.log('✅ Received AI response:', data.aiMessage.content);

        // Add AI response to messages (this will replace the start message)
        setMessages(prev => {
          // Remove the start message and add the actual AI response
          const withoutStartMessage = prev.filter(msg => msg.id !== startMessage.id);
          const newMessages = [...withoutStartMessage, {
            id: data.aiMessage.id,
            type: 'ai' as const,
            content: data.aiMessage.content,
            timestamp: new Date(data.aiMessage.timestamp)
          }];

          // Save AI response to database
          if (currentBuildResultUuid) {
            console.log('💾 Saving AI response to database');
            saveChatHistory(currentBuildResultUuid, newMessages).catch(error => {
              console.error('❌ Error saving AI response:', error);
            });
          }

          console.log('✅ Chat conversation updated, total messages:', newMessages.length);
          return newMessages;
        });

        // Extract and update files in Monaco workspace using backend API
        if (data.fullResponse && uuid) {
          try {
            console.log('📁 Extracting files from chat response using backend API');

            const token = localStorage.getItem('token');
            const extractResponse = await fetch(`http://localhost:3001/api/workspace/workspace_${uuid}/extract-chat`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
              },
              body: JSON.stringify({
                chatResponse: data.fullResponse
              })
            });

            if (extractResponse.ok) {
              const extractData = await extractResponse.json();
              if (extractData.success && extractData.data.files && Object.keys(extractData.data.files).length > 0) {
                console.log('📁 Extracted files from chat response:', Object.keys(extractData.data.files));

                // Update Monaco workspace
                const monacoWorkspace = (window as any)[`monacoWorkspace_${uuid}`];
                if (monacoWorkspace && monacoWorkspace.updateFiles) {
                  monacoWorkspace.updateFiles(extractData.data.files);
                  console.log('✅ Monaco workspace updated with new files');
                } else {
                  // Fallback: dispatch event
                  window.dispatchEvent(new CustomEvent('monaco-workspace-update', {
                    detail: { files: extractData.data.files }
                  }));
                  console.log('✅ Dispatched Monaco workspace update event');
                }
              } else {
                console.log('📁 No files found in chat response');
              }
            } else {
              console.error('❌ Failed to extract files from chat response:', extractResponse.status);
            }
          } catch (extractError) {
            console.error('❌ Error extracting files from chat response:', extractError);
          }
        }

        // Add "complete" message after a short delay
        setTimeout(() => {
          const completeMessage: ChatMessage = {
            id: `complete-${Date.now()}`,
            type: 'ai',
            content: '✅ Request completed successfully! Your files have been updated.',
            timestamp: new Date()
          };
          setMessages(prev => {
            const newMessages = [...prev, completeMessage];

            // Save success message to database
            if (currentBuildResultUuid) {
              console.log('💾 Saving success message to database');
              saveChatHistory(currentBuildResultUuid, newMessages).catch(error => {
                console.error('❌ Error saving success message:', error);
              });
            }

            // The completion message will be saved by the debounced auto-save
            // No need for immediate save to avoid conflicts
            console.log('✅ Completion message added, will be saved by auto-save');

            return newMessages;
          });
        }, 1000);
      }
    } catch (error) {
      console.error('Error sending chat message:', error);
      // Remove start message and add error message
      setMessages(prev => {
        const withoutStartMessage = prev.filter(msg => msg.id !== startMessage.id);
        return [...withoutStartMessage, {
          id: Date.now().toString(),
          type: 'ai',
          content: 'Sorry, there was an error processing your message. Please try again.',
          timestamp: new Date()
        }];
      });
    } finally {
      setIsSubmitting(false);
    }
  };






  // File management handlers
  const handleWorkspaceReady = (workspaceId: string) => {
    console.log('Workspace ready:', workspaceId);
    setCurrentWorkspaceId(workspaceId);

    // Reset refresh flag after workspace is ready
    if (shouldRefreshWorkspace) {
      setShouldRefreshWorkspace(false);
    }
  };

  // Chatbox collapse/expand handlers
  const handleChatboxClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent any parent click handlers from firing
    if (!isChatboxExpanded) {
      setIsChatboxExpanded(true);
    }
  };

  const toggleChatbox = () => {
    setIsChatboxExpanded(!isChatboxExpanded);
  };

  return (
    <>
      <Header />

      <Box
        sx={{
          minHeight: 'calc(100vh - 64px)',
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
          pb: '200px' // Fixed padding for chatbox
        }}
      >
        {/* Loading State */}
        {isLoading && (
          <Container maxWidth="lg" sx={{ pt: 4 }}>
            <Paper elevation={6} sx={{ borderRadius: 4, p: 4, textAlign: 'center' }}>
              <CircularProgress size={60} sx={{ color: '#667eea', mb: 2 }} />
              <Typography variant="h6" sx={{ mb: 1 }}>
                Loading Interview Configuration...
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {uuid ? `Loading project with ID: ${uuid}` : 'Preparing your build environment...'}
              </Typography>
            </Paper>
          </Container>
        )}

        {/* Error State */}
        {error && !isLoading && (
          <Container maxWidth="lg" sx={{ pt: 4 }}>
            <Paper elevation={6} sx={{ borderRadius: 4, p: 4 }}>
              <Alert severity="error" sx={{ mb: 2 }}>
                <Typography variant="h6" sx={{ mb: 1 }}>
                  Failed to Load Project
                </Typography>
                <Typography variant="body2">
                  {error}
                </Typography>
              </Alert>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
                <Button
                  variant="outlined"
                  startIcon={<ArrowBackIcon />}
                  onClick={() => navigate('/')}
                >
                  Back to Home
                </Button>
                <Button
                  variant="contained"
                  onClick={() => window.location.reload()}
                  sx={{ bgcolor: '#667eea', '&:hover': { bgcolor: '#5a6fd8' } }}
                >
                  Retry
                </Button>
              </Box>
            </Paper>
          </Container>
        )}

        {/* Main Content */}
        {!isLoading && !error && (
          <Box sx={{
            position: 'relative',
            height: 'calc(100vh - 64px)',
            display: 'flex',
            flexDirection: 'column'
          }}>
            {/* Top Navigation Bar */}
            <Container maxWidth="xl" sx={{ flexShrink: 0 }}>
              <Box sx={{ py: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <Button
                  onClick={() => {
                    if (uuid) {
                      let functionalAnswers = {};
                      let technicalAnswers = {};

                      if (interviewData?.projectData) {
                        const flatAnswers = transformNewSchemaToFlatAnswers(interviewData.projectData);
                        functionalAnswers = flatAnswers.functionalAnswers;
                        technicalAnswers = flatAnswers.technicalAnswers;
                      } else if (locationState?.functionalAnswers || locationState?.technicalAnswers) {
                        functionalAnswers = locationState.functionalAnswers || {};
                        technicalAnswers = locationState.technicalAnswers || {};
                      }

                      navigate(`/interview/${uuid}`, {
                        state: {
                          prompt: userPrompt,
                          functionalAnswers,
                          technicalAnswers
                        }
                      });
                    } else {
                      navigate('/interview');
                    }
                  }}
                  startIcon={<ArrowBackIcon />}
                  sx={{ color: '#667eea' }}
                >
                  Edit Interview
                </Button>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    sx={{
                      bgcolor: '#4361ee', // Dark blue
                      '&:hover': { bgcolor: '#0d47a1' }, // Darker blue on hover
                      color: 'white',
                      fontWeight: 'bold'
                    }}
                  >
                    Publish
                  </Button>
                </Box>
              </Box>
            </Container>

            {/* Workspace Section - Fixed Height */}
            <Container
              maxWidth="xl"
              sx={{
                flexShrink: 0, // Don't shrink
                display: 'flex',
                flexDirection: 'column',
                mb: { xs: 3, sm: 4, md: 5 }
              }}
            >
              <Paper
                elevation={6}
                sx={{
                  height: '820px', // Fixed height - won't change with window resize
                  borderRadius: 3,
                  overflow: 'hidden',
                  display: 'flex',
                  flexDirection: 'column'
                }}
              >
                {/* Workspace Header */}
                <Box
                  sx={{
                    background: '#667eea',
                    color: 'white',
                    p: 1.5,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <WorkspaceIcon sx={{ mr: 1 }} />
                    <Typography variant="h6" fontWeight="bold">
                      Monaco Editor Workspace
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {uuid && (
                      <Typography variant="caption" sx={{ opacity: 0.8 }}>
                        Session: {uuid.substring(0, 8)}...
                      </Typography>
                    )}
                  </Box>
                </Box>

                {/* Workspace Content */}
                <Box
                  sx={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column'
                  }}
                >
                  {!hasCodeGenerated ? (
                    <Box
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#666',
                        p: 2
                      }}
                    >
                      <CircularProgress size={40} sx={{ mb: 2 }} />
                      <Typography variant="h6">
                        Preparing your coding environment...
                      </Typography>
                      <Typography variant="body2" sx={{ mt: 1 }}>
                        Please wait while AI generates your project
                      </Typography>
                    </Box>
                  ) : (
                    <Box sx={{
                      flex: 1,
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 2,
                      height: '740px'
                    }}>
                      {/* Monaco Workspace */}
                      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                        <MonacoWorkspace
                          interviewUuid={uuid}
                          onWorkspaceReady={handleWorkspaceReady}
                          onError={(error: string) => {
                            console.error('Workspace error:', error);
                          }}
                          height="100%"
                        />
                      </Box>

                      {/* Live Preview */}
                      <Box sx={{
                        flex: 1,
                        display: 'flex',
                        flexDirection: 'column',
                        backgroundColor: '#f5f5f5',
                        borderRadius: 1,
                        overflow: 'hidden',
                        border: '1px solid #e0e0e0'
                      }}>
                        {/* Preview Header */}
                        <Box sx={{
                          p: 1,
                          backgroundColor: '#2d2d2d',
                          borderBottom: '1px solid #444',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between'
                        }}>
                          <Typography variant="body2" sx={{ color: '#cccccc', fontWeight: 'bold' }}>
                            Live Preview (localhost:8080)
                          </Typography>
                          <IconButton
                            size="small"
                            onClick={() => {
                              const iframe = document.getElementById('live-preview-iframe') as HTMLIFrameElement;
                              if (iframe) {
                                iframe.src = iframe.src; // Refresh iframe
                              }
                            }}
                            sx={{ color: '#cccccc', '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' } }}
                          >
                            <RefreshIcon fontSize="small" />
                          </IconButton>
                        </Box>

                        {/* Preview Content */}
                        <Box sx={{ flex: 1, position: 'relative', overflow: 'hidden' }}>
                          <iframe
                            id="live-preview-iframe"
                            src="http://localhost:8080"
                            style={{
                              width: '100%',
                              height: '100%',
                              border: 'none',
                              backgroundColor: 'white'
                            }}
                            title="Live Preview"
                            onError={(e) => {
                              console.error('Preview iframe error:', e);
                            }}
                          />
                        </Box>
                      </Box>
                    </Box>
                  )}
                </Box>
              </Paper>
            </Container>

            {/* Fixed Floating Chatbox at Bottom */}
            <Box
              onClick={handleChatboxClick}
              sx={{
                position: 'fixed',
                bottom: 20,
                left: '50%',
                transform: 'translateX(-50%)',
                width: { xs: '90%', sm: '70%', md: '50%' }, // Reduced width: 90% mobile, 70% tablet, 50% desktop
                maxWidth: '800px', // Reduced max width for better proportions
                zIndex: 1000,
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
                borderRadius: 3,
                cursor: !isChatboxExpanded ? 'pointer' : 'default' // Only show pointer when collapsed for click-to-expand
              }}
            >
              <Paper
                elevation={12}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  borderRadius: 3,
                  overflow: 'hidden',
                  height: isChatboxExpanded ? '380px' : 'auto', // Dynamic height based on expanded state
                  maxHeight: isChatboxExpanded ? '380px' : '80px', // Collapsed height for input only
                  backgroundColor: 'white',
                  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)', // Smooth animation
                  border: '1px solid rgba(102, 126, 234, 0.1)', // Subtle border for definition
                  '&:hover': {
                    transform: isChatboxExpanded ? 'translateY(-2px)' : 'translateY(-1px)',
                    boxShadow: '0 12px 40px rgba(0, 0, 0, 0.25)',
                    borderColor: 'rgba(102, 126, 234, 0.2)'
                  }
                }}
              >
                {/* Chat Input at Top */}
                <Box
                  component="form"
                  onSubmit={handleSubmit}
                  sx={{
                    p: 2,
                    borderBottom: '1px solid #e0e0e0',
                    backgroundColor: 'white',
                    position: 'relative'
                  }}
                >
                  {/* Floating indicator */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: -6,
                      left: '50%',
                      transform: 'translateX(-50%)',
                      width: 40,
                      height: 4,
                      backgroundColor: '#667eea',
                      borderRadius: 2,
                      opacity: 0.6
                    }}
                  />
                  <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                    <AIIcon sx={{ color: '#667eea', mr: 1 }} />
                    <TextField
                      fullWidth
                      size="small"
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSubmit(e as any);
                        }
                      }}
                      placeholder={hasCodeGenerated ? "Ask AI to modify your project..." : "Please wait for initial code generation..."}
                      disabled={isSubmitting || !hasCodeGenerated}
                      multiline
                      maxRows={3}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          backgroundColor: '#f8f9fa'
                        }
                      }}
                    />
                    <IconButton
                      type="submit"
                      disabled={!prompt.trim() || isSubmitting || !hasCodeGenerated}
                      sx={{
                        bgcolor: '#667eea',
                        color: 'white',
                        '&:hover': { bgcolor: '#5a6fd8' },
                        '&:disabled': { bgcolor: '#ccc' },
                        borderRadius: 2,
                        p: 1.5
                      }}
                    >
                      <SendIcon />
                    </IconButton>
                    {/* Expand/Collapse Toggle Button */}
                    <IconButton
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleChatbox();
                      }}
                      sx={{
                        bgcolor: 'rgba(102, 126, 234, 0.1)',
                        color: '#667eea',
                        '&:hover': { bgcolor: 'rgba(102, 126, 234, 0.2)' },
                        borderRadius: 2,
                        p: 1.5
                      }}
                    >
                      {isChatboxExpanded ? <ExpandMoreIcon /> : <ExpandLessIcon />}
                    </IconButton>
                    {isGeneratingCode && (
                      <CircularProgress size={20} sx={{ color: '#667eea', ml: 1 }} />
                    )}
                  </Box>
                </Box>

                {/* Chat Messages Below Input - Only show when expanded */}
                {isChatboxExpanded && (
                  <Box
                    sx={{
                      flex: 1,
                      p: 2,
                      overflowY: 'auto',
                      backgroundColor: '#f9f9f9',
                      maxHeight: '280px', // Reduced to fit new chatbox height
                      minHeight: '180px', // Reduced minimum height
                      opacity: isChatboxExpanded ? 1 : 0,
                      transition: 'opacity 0.3s ease-in-out'
                    }}
                  >
                    {messages.map((message) => (
                      <Box
                        key={message.id}
                        sx={{
                          mb: 2,
                          display: 'flex',
                          justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start'
                        }}
                      >
                        <Paper
                          elevation={2}
                          sx={{
                            p: 2,
                            maxWidth: '80%',
                            backgroundColor: message.type === 'user' ? '#667eea' : 'white',
                            color: message.type === 'user' ? 'white' : 'black',
                            borderRadius: 2
                          }}
                        >
                          <Typography variant="body2">
                            {message.content}
                          </Typography>
                          <Typography
                            variant="caption"
                            sx={{
                              opacity: 0.7,
                              display: 'block',
                              mt: 0.5
                            }}
                          >
                            {message.timestamp.toLocaleTimeString()}
                          </Typography>
                        </Paper>
                      </Box>
                    ))}
                    {isSubmitting && (
                      <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
                        <Paper
                          elevation={2}
                          sx={{
                            p: 2,
                            backgroundColor: 'white',
                            borderRadius: 2,
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          <CircularProgress size={16} sx={{ mr: 1 }} />
                          <Typography variant="body2">AI is thinking...</Typography>
                        </Paper>
                      </Box>
                    )}
                    <div ref={messagesEndRef} />
                  </Box>
                )}
              </Paper>
            </Box>
          </Box>
        )}
      </Box>
    </>
  );
};

export default Build;